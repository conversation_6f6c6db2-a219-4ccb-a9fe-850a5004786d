'use client';

import { useState } from 'react';
import GamePricesWidget from '@/components/game/GamePricesWidget';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

// Test game IDs from your database
const TEST_GAMES = [
  { id: '3e6c0866-dee4-47a0-b45e-26beadf29919', name: 'Elden Ring: Nightreign' },
  { id: 'fc4ff33c-f344-4616-87be-f28c1a20002a', name: 'Metal Gear & Metal Gear 2: Solid Snake' },
  { id: '292fa7b6-b951-4bc2-8b7b-fd15f23afa0d', name: 'LocoRoco' }
];

export default function TestPricesPage() {
  const [selectedGame, setSelectedGame] = useState(TEST_GAMES[0]);
  const [customGameId, setCustomGameId] = useState('');
  const [customGameName, setCustomGameName] = useState('');

  const handleCustomTest = () => {
    if (customGameId && customGameName) {
      setSelectedGame({ id: customGameId, name: customGameName });
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <Card className="bg-slate-800/60 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-2xl text-white flex items-center gap-2">
              🧪 Price Fetching Test - Development Mode
              <Badge variant="secondary" className="bg-green-900/50 text-green-300">
                Direct Store URLs
              </Badge>
            </CardTitle>
            <p className="text-slate-400">
              Testing CJ Fanatical integration with direct store URLs (no affiliate tracking in dev mode)
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Predefined Games */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Test Games</h3>
                <div className="space-y-2">
                  {TEST_GAMES.map((game) => (
                    <Button
                      key={game.id}
                      variant={selectedGame.id === game.id ? "default" : "outline"}
                      onClick={() => setSelectedGame(game)}
                      className="w-full justify-start"
                    >
                      {game.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Custom Game Test */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Custom Game Test</h3>
                <div className="space-y-2">
                  <Input
                    placeholder="Game ID (UUID)"
                    value={customGameId}
                    onChange={(e) => setCustomGameId(e.target.value)}
                    className="bg-slate-700/50 border-slate-600"
                  />
                  <Input
                    placeholder="Game Name"
                    value={customGameName}
                    onChange={(e) => setCustomGameName(e.target.value)}
                    className="bg-slate-700/50 border-slate-600"
                  />
                  <Button 
                    onClick={handleCustomTest}
                    disabled={!customGameId || !customGameName}
                    className="w-full"
                  >
                    Test Custom Game
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Test */}
        <Card className="bg-slate-800/60 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-lg text-white">
              Currently Testing: {selectedGame.name}
            </CardTitle>
            <p className="text-sm text-slate-400">Game ID: {selectedGame.id}</p>
          </CardHeader>
        </Card>

        {/* Price Widget Test */}
        <GamePricesWidget 
          gameId={selectedGame.id}
          gameName={selectedGame.name}
          className="w-full"
        />

        {/* Debug Info */}
        <Card className="bg-slate-800/60 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-lg text-white">🔧 Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-semibold text-slate-300 mb-2">Expected Behavior:</h4>
                <ul className="space-y-1 text-slate-400">
                  <li>• Fanatical prices should appear with red styling</li>
                  <li>• Links should go directly to store (no affiliate tracking)</li>
                  <li>• Mock prices should be realistic and consistent</li>
                  <li>• Multiple CJ stores may appear (Fanatical, Humble Bundle, GamesPlanet)</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-slate-300 mb-2">Troubleshooting:</h4>
                <ul className="space-y-1 text-slate-400">
                  <li>• Check browser console for detailed logs</li>
                  <li>• Use "Forçar" button to trigger fresh price fetch</li>
                  <li>• Verify links open to correct store search pages</li>
                  <li>• Test different regions using the region selector</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
