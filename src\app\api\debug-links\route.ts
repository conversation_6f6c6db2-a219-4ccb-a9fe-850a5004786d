import { NextRequest, NextResponse } from 'next/server';
import { cjApiService } from '@/lib/services/cjApiService';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const testGame = url.searchParams.get('game') || 'Cyberpunk 2077';
    
    console.log(`🔗 Testing link generation for: "${testGame}"`);
    
    // Test CJ affiliate links
    const fanaticalPrice = await cjApiService.getFanaticalPrice(testGame, 'us');
    
    // Test direct store URLs
    const directUrls = {
      fanatical: `https://www.fanatical.com/en/search?search=${encodeURIComponent(testGame)}`,
      steam: `https://store.steampowered.com/search/?term=${encodeURIComponent(testGame)}`,
      kinguin: `https://www.kinguin.net/search?q=${encodeURIComponent(testGame)}`
    };
    
    return NextResponse.json({
      success: true,
      testGame,
      affiliateLinks: {
        fanatical: fanaticalPrice ? {
          store_url: fanaticalPrice.store_url,
          affiliate_url: fanaticalPrice.affiliate_url,
          isWorking: true
        } : { isWorking: false, error: 'No Fanatical price generated' }
      },
      directUrls,
      linkValidation: {
        fanaticalAffiliate: fanaticalPrice?.affiliate_url ? 'Generated' : 'Failed',
        fanaticalDirect: fanaticalPrice?.store_url ? 'Generated' : 'Failed'
      },
      troubleshooting: {
        cjConfigured: cjApiService.isConfigured(),
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('💥 Link debug test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
