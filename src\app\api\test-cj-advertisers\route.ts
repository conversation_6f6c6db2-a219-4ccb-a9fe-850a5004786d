import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const CJ_DEVELOPER_KEY = process.env.CJ_DEVELOPER_KEY;
    const CJ_WEBSITE_ID = process.env.CJ_WEBSITE_ID;
    
    if (!CJ_DEVELOPER_KEY || !CJ_WEBSITE_ID) {
      return NextResponse.json({
        success: false,
        error: 'CJ API credentials not configured'
      });
    }

    console.log('🔍 Testing CJ API - Checking active advertisers...');

    // Test CJ Advertiser Lookup API to see which advertisers you have access to
    const advertiserUrl = `https://advertiser-lookup.api.cj.com/v3/advertiser-lookup`;
    
    const headers = {
      'Authorization': `Bearer ${CJ_DEVELOPER_KEY}`,
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    };

    console.log('📡 Making request to CJ Advertiser Lookup API...');
    
    const response = await fetch(advertiserUrl, {
      method: 'GET',
      headers: headers
    });

    console.log(`📊 CJ API Response Status: ${response.status}`);
    console.log(`📊 CJ API Response Headers:`, Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log(`📄 CJ API Raw Response:`, responseText.substring(0, 500));

    let parsedData;
    try {
      parsedData = JSON.parse(responseText);
    } catch (e) {
      console.log('⚠️ Response is not JSON, might be HTML or error page');
      parsedData = null;
    }

    // Also test the Product Search API
    console.log('🔍 Testing Product Search API...');
    const productSearchUrl = `https://product-search.api.cj.com/v2/product-search`;
    
    const productResponse = await fetch(`${productSearchUrl}?website-id=${CJ_WEBSITE_ID}&keywords=game`, {
      method: 'GET',
      headers: headers
    });

    console.log(`🎮 Product Search Response Status: ${productResponse.status}`);
    const productResponseText = await productResponse.text();
    console.log(`🎮 Product Search Raw Response:`, productResponseText.substring(0, 500));

    return NextResponse.json({
      success: true,
      tests: {
        advertiserLookup: {
          url: advertiserUrl,
          status: response.status,
          isJson: !!parsedData,
          data: parsedData,
          rawResponse: responseText.substring(0, 1000)
        },
        productSearch: {
          url: productSearchUrl,
          status: productResponse.status,
          rawResponse: productResponseText.substring(0, 1000)
        }
      },
      credentials: {
        hasApiKey: !!CJ_DEVELOPER_KEY,
        hasWebsiteId: !!CJ_WEBSITE_ID,
        websiteId: CJ_WEBSITE_ID
      },
      instructions: {
        message: "Check if you have active advertiser relationships in your CJ account",
        steps: [
          "1. Login to your CJ Affiliate account",
          "2. Go to 'Account' > 'Advertiser Relationships'", 
          "3. Look for Fanatical, Humble Bundle, GamesPlanet",
          "4. Apply to their affiliate programs if not already active",
          "5. Wait for approval before API will return their products"
        ]
      }
    });

  } catch (error) {
    console.error('💥 CJ API test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
